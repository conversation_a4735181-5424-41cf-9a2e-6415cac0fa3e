#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Emma Studio build for Vercel...');

try {
  // Step 1: Build the client
  console.log('📦 Building client application...');
  execSync('cd client && npm install && npm run build', { stdio: 'inherit' });
  
  // Step 2: Verify build output exists
  const clientDistPath = path.join(__dirname, 'client', 'dist');
  if (!fs.existsSync(clientDistPath)) {
    throw new Error('Client build failed - dist directory not found');
  }
  
  console.log('✅ Build completed successfully!');
  console.log(`📁 Build output available at: ${clientDistPath}`);
  
  // List contents for verification
  const files = fs.readdirSync(clientDistPath);
  console.log('📋 Build output contents:', files.slice(0, 10).join(', '));
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
