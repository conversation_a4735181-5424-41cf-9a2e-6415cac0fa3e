"use client";

import React from "react";
import { ArrowLef<PERSON>, ArrowRight } from "lucide-react";
import { useEffect, useState } from "react";

// Utility function to check if a file is a video
const isVideoFile = (url: string): boolean => {
  const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.mkv', '.m4v', '.3gp'];
  const lowerUrl = url.toLowerCase();
  return videoExtensions.some(ext => lowerUrl.includes(ext)) || lowerUrl.includes('video');
};

import { Button } from "@/components/ui/button";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

export interface Gallery4Item {
  id: string;
  title: string;
  description: string;
  href: string;
  image: string;
}

export interface Gallery4Props {
  title?: React.ReactNode;
  description?: string;
  items: Gallery4Item[];
  autoplay?: boolean;
  autoplayInterval?: number;
  badgeText?: string;
  exploreButtonText?: string;
}

const Gallery4 = ({
  title = "Herramientas",
  description = "Descubre las herramientas profesionales de Emma Studio",
  items = [],
  autoplay = true,
  autoplayInterval = 4000,
  badgeText = "🛠️ Herramientas Profesionales",
  exploreButtonText = "Explorar herramienta",
}: Gallery4Props) => {
  const [carouselApi, setCarouselApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoplayPaused, setIsAutoplayPaused] = useState(false);
  const [isCarouselReady, setIsCarouselReady] = useState(false);
  const [videoErrors, setVideoErrors] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!carouselApi) {
      setIsCarouselReady(false);
      return;
    }

    setIsCarouselReady(true);

    const updateSelection = () => {
      setCanScrollPrev(carouselApi.canScrollPrev());
      setCanScrollNext(carouselApi.canScrollNext());
      setCurrentSlide(carouselApi.selectedScrollSnap());
    };

    updateSelection();
    carouselApi.on("select", updateSelection);
    carouselApi.on("reInit", updateSelection);

    return () => {
      carouselApi.off("select", updateSelection);
      carouselApi.off("reInit", updateSelection);
    };
  }, [carouselApi]);

  // Autoplay functionality
  useEffect(() => {
    if (!carouselApi || !autoplay || isAutoplayPaused) {
      return;
    }

    const autoplayTimer = setInterval(() => {
      if (carouselApi.canScrollNext()) {
        carouselApi.scrollNext();
      } else {
        // Si llegamos al final, volver al inicio
        carouselApi.scrollTo(0);
      }
    }, autoplayInterval);

    return () => {
      clearInterval(autoplayTimer);
    };
  }, [carouselApi, autoplay, autoplayInterval, isAutoplayPaused]);

  // Pausar autoplay temporalmente cuando el usuario interactúa
  const handleManualNavigation = (action: () => void) => {
    if (!carouselApi) {
      console.warn('Carousel API not ready yet');
      return;
    }

    setIsAutoplayPaused(true);
    action();
    // Reanudar autoplay después de 8 segundos
    setTimeout(() => {
      setIsAutoplayPaused(false);
    }, 8000);
  };

  // Manual navigation functions with proper error handling
  const handlePrevious = () => {
    handleManualNavigation(() => {
      if (carouselApi && carouselApi.canScrollPrev()) {
        carouselApi.scrollPrev();
      }
    });
  };

  const handleNext = () => {
    handleManualNavigation(() => {
      if (carouselApi && carouselApi.canScrollNext()) {
        carouselApi.scrollNext();
      }
    });
  };

  const handleGoToSlide = (index: number) => {
    handleManualNavigation(() => {
      if (carouselApi) {
        carouselApi.scrollTo(index);
      }
    });
  };

  // Handle video loading errors
  const handleVideoError = (itemId: string, error: any) => {
    console.error('Video failed to load:', itemId, error);
    setVideoErrors(prev => new Set(prev).add(itemId));
  };

  return (
    <section className="py-20 sm:py-24 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm px-8 py-4 rounded-2xl border border-white/20 shadow-lg text-sm font-semibold text-gray-800 mb-8">
            {badgeText}
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight max-w-4xl mx-auto">
            {title}
          </h2>
          <p className="text-lg font-normal text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6">
            {description}
          </p>

          {/* Navigation Controls */}
          <div className="flex justify-center gap-4 relative z-[60]">
            <button
              onClick={handlePrevious}
              disabled={!isCarouselReady || !canScrollPrev}
              className="w-14 h-14 rounded-full bg-white/80 backdrop-blur-sm shadow-xl border border-gray-200 hover:shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gradient-to-r hover:from-[#3018ef] hover:to-[#dd3a5a] hover:text-white relative z-[60]"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <button
              onClick={handleNext}
              disabled={!isCarouselReady || !canScrollNext}
              className="w-14 h-14 rounded-full bg-white/80 backdrop-blur-sm shadow-xl border border-gray-200 hover:shadow-2xl hover:scale-110 transition-all duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gradient-to-r hover:from-[#3018ef] hover:to-[#dd3a5a] hover:text-white relative z-[60]"
            >
              <ArrowRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
      <div className="w-full">
        <Carousel
          setApi={setCarouselApi}
          opts={{
            breakpoints: {
              "(max-width: 768px)": {
                dragFree: true,
              },
            },
          }}
        >
          <CarouselContent className="ml-0 2xl:ml-[max(8rem,calc(50vw-700px))] 2xl:mr-[max(0rem,calc(50vw-700px))]">
            {items.map((item) => (
              <CarouselItem
                key={item.id}
                className="max-w-[320px] pl-[20px] lg:max-w-[360px]"
              >
                <a href={item.href} className="group block">
                  <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl hover:scale-105 transition-all duration-300 overflow-hidden h-full min-h-[27rem]">
                    <div className="relative h-48 overflow-hidden">
                      {isVideoFile(item.image) && !videoErrors.has(item.id) ? (
                        <video
                          src={item.image}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                          autoPlay
                          muted
                          loop
                          playsInline
                          preload="metadata"
                          controls={false}
                          disablePictureInPicture
                          onError={(e) => {
                            handleVideoError(item.id, e);
                          }}
                          onLoadStart={() => {
                            console.log('Video loading started:', item.image);
                          }}
                          onCanPlay={() => {
                            console.log('Video can play:', item.image);
                          }}
                        />
                      ) : (
                        <div className="relative w-full h-full">
                          <img
                            src={isVideoFile(item.image) && videoErrors.has(item.id)
                              ? "https://images.unsplash.com/photo-1536735561749-fc87494598cb?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=******************************************************&ixlib=rb-4.0.3&q=80&w=1080"
                              : item.image}
                            alt={item.title}
                            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                            onError={(e) => {
                              console.error('Image failed to load:', item.image);
                            }}
                          />
                          {isVideoFile(item.image) && videoErrors.has(item.id) && (
                            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                              <div className="text-white text-center">
                                <div className="text-2xl mb-2">🎬</div>
                                <div className="text-sm">Video Studio</div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-black text-gray-900 mb-3">
                        {item.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4 line-clamp-3">
                        {item.description}
                      </p>
                      <div className="flex items-center text-[#3018ef] font-bold text-sm">
                        {exploreButtonText}{" "}
                        <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
                      </div>
                    </div>
                  </div>
                </a>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
        <div className="mt-12 flex justify-center gap-3 relative z-[60]">
          {items.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 relative z-[60] ${
                currentSlide === index
                  ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] scale-125"
                  : "bg-gray-300 hover:bg-gray-400 hover:scale-110"
              }`}
              onClick={() => handleGoToSlide(index)}
              disabled={!isCarouselReady}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export { Gallery4 };
