"use client"

import { useState, useEffect } from "react"
import { Bot, Search, PenTool, Camera, BarChart3, Globe, MessageSquare, Zap } from "lucide-react"
import CircularTestimonials from "@/components/ui/circular-testimonials"
import { But<PERSON> } from "@/components/ui/button"
import { SubtleGradient } from "@/components/ui/subtle-gradients"
import { useLanguage } from "@/contexts/LanguageContext"

// Función para obtener agentes traducidos
const getEmmaAgents = (currentLanguage: string) => [
  {
    name: "<PERSON>",
    designation: currentLanguage === 'en' ? "Digital Prospecting Specialist" : "Especialista en Prospección Digital",
    quote: currentLanguage === 'en'
      ? "I find business emails and execute outreach campaigns that generate responses. I use advanced prospecting techniques to find high-quality potential clients."
      : "Encuentro correos empresariales y ejecuto campañas de outreach que generan respuestas. Utilizo técnicas avanzadas de prospección para encontrar clientes potenciales de alta calidad.",
    src: "/agents/María.png",
    capabilities: currentLanguage === 'en'
      ? ["Lead Generation", "Email Finding", "Outreach Campaigns", "Digital Prospecting"]
      : ["Lead Generation", "Email Finding", "Outreach Campaigns", "Prospección Digital"],
    rating: 4.8,
    reviews: 156
  },
  {
    name: "Diego Morales",
    designation: currentLanguage === 'en' ? "Appointments and Meetings Coordinator" : "Coordinador de Citas y Reuniones",
    quote: currentLanguage === 'en'
      ? "I manage your calendar and schedule business meetings automatically. I optimize schedules and coordinate appointments to maximize your business productivity."
      : "Gestiono tu calendario y agenda reuniones comerciales automáticamente. Optimizo horarios y coordino citas para maximizar tu productividad empresarial.",
    src: "/agents/Diego.png",
    capabilities: currentLanguage === 'en'
      ? ["Calendar Management", "Meeting Scheduling", "Automation", "Time Optimization"]
      : ["Calendar Management", "Meeting Scheduling", "Automation", "Time Optimization"],
    rating: 4.7,
    reviews: 142
  },
  {
    name: "Andrés Herrera",
    designation: currentLanguage === 'en' ? "Digital Community Manager" : "Community Manager Digital",
    quote: currentLanguage === 'en'
      ? "I manage your social media and provide 24/7 support to your community. I create engaging content and keep active interaction with your audience."
      : "Gestiono tus redes sociales y brindo soporte a tu comunidad 24/7. Creo contenido engaging y mantengo activa la interacción con tu audiencia.",
    src: "/agents/Andrés.png",
    capabilities: currentLanguage === 'en'
      ? ["Social Media Management", "Community Support", "Content Creation", "Engagement"]
      : ["Social Media Management", "Community Support", "Content Creation", "Engagement"],
    rating: 4.6,
    reviews: 189
  },
  {
    name: "Roberto Silva",
    designation: currentLanguage === 'en' ? "Customer Service Specialist" : "Especialista en Atención al Cliente",
    quote: currentLanguage === 'en'
      ? "I respond to customer emails and organize the inbox by priorities. I manage customer service via email with personalized and efficient responses."
      : "Respondo emails de clientes y organizo la bandeja de entrada por prioridades. Gestiono la atención al cliente por email con respuestas personalizadas y eficientes.",
    src: "/agents/Roberto.png",
    capabilities: currentLanguage === 'en'
      ? ["Customer Support", "Email Management", "Issue Resolution", "Priority Organization"]
      : ["Customer Support", "Email Management", "Issue Resolution", "Priority Organization"],
    rating: 4.5,
    reviews: 134
  },
  {
    name: "Miguel Torres",
    designation: currentLanguage === 'en' ? "WhatsApp Sales Executive" : "Ejecutivo de Ventas WhatsApp",
    quote: currentLanguage === 'en'
      ? "I handle leads via WhatsApp and pre-qualify sales opportunities. I manage sales conversations professionally and guide prospects toward conversion."
      : "Atiendo leads por WhatsApp y pre-califico oportunidades de venta. Gestiono conversaciones de ventas de manera profesional y guío prospectos hacia la conversión.",
    src: "/agents/Miguel.png",
    capabilities: currentLanguage === 'en'
      ? ["WhatsApp Sales", "Lead Qualification", "Sales Conversations", "Conversion Optimization"]
      : ["WhatsApp Sales", "Lead Qualification", "Sales Conversations", "Conversion Optimization"],
    rating: 4.6,
    reviews: 167
  },
  {
    name: "Laura Vásquez",
    designation: currentLanguage === 'en' ? "Phone Sales Representative" : "Representante de Ventas Telefónicas",
    quote: currentLanguage === 'en'
      ? "I handle incoming calls and execute cold calling campaigns. I optimize phone sales processes and provide strategic support to close more deals."
      : "Manejo llamadas entrantes y ejecuto campañas de cold calling. Optimizo procesos de ventas telefónicas y proporciono soporte estratégico para cerrar más deals.",
    src: "/agents/Laura.png",
    capabilities: currentLanguage === 'en'
      ? ["Cold Calling", "Inbound Calls", "Sales Process", "Phone Sales"]
      : ["Cold Calling", "Inbound Calls", "Sales Process", "Phone Sales"],
    rating: 4.4,
    reviews: 123
  },
  {
    name: "Jorge Flores",
    designation: currentLanguage === 'en' ? "Content Creation Specialist" : "Especialista en Creación de Contenido",
    quote: currentLanguage === 'en'
      ? "I transform ideas into captivating narratives. I create SEO-optimized content that connects with your audience and converts visitors into customers."
      : "Transformo ideas en narrativas cautivadoras. Creo contenido optimizado para SEO que conecta con tu audiencia y convierte visitantes en clientes.",
    src: "/agents/Jorge Flores.png",
    capabilities: currentLanguage === 'en'
      ? ["Content Creation", "SEO Writing", "Copywriting", "Brand Storytelling"]
      : ["Content Creation", "SEO Writing", "Copywriting", "Brand Storytelling"],
    rating: 4.7,
    reviews: 203
  },
  {
    name: "Emma AI",
    designation: currentLanguage === 'en' ? "AI Orchestration Chief" : "Jefa de Orquestación AI",
    quote: currentLanguage === 'en'
      ? "I coordinate all specialized agents to create comprehensive marketing strategies. I analyze context and orchestrate teams to achieve effective campaigns."
      : "Coordino todos los agentes especializados para crear estrategias de marketing comprehensivas. Analizo el contexto y orquesto equipos para lograr campañas efectivas.",
    src: "/agents/Emma agente.png",
    capabilities: currentLanguage === 'en'
      ? ["Team Coordination", "Strategy Planning", "Multi-Agent Management", "Campaign Orchestration"]
      : ["Team Coordination", "Strategy Planning", "Multi-Agent Management", "Campaign Orchestration"],
    rating: 4.9,
    reviews: 312
  }
]

export function AgentShowcase() {
  const { t, currentLanguage } = useLanguage()
  const [isVisible, setIsVisible] = useState(false)

  // Obtener agentes traducidos
  const emmaAgents = getEmmaAgents(currentLanguage)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const section = document.getElementById('agents-section')
    if (section) observer.observe(section)

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="agents-section"
      className="py-20 sm:py-24 bg-white relative overflow-hidden"
    >
      <SubtleGradient variant="blue" position="bottom-left" size="md" />
      {/* Clean Background */}
      <div className="absolute inset-0 bg-white" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-12">
          <div
            className={`transition-all duration-700 ease-out ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="inline-block bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm px-8 py-4 rounded-2xl border border-white/20 shadow-lg text-sm font-semibold text-gray-800 mb-8">
              <Zap className="w-4 h-4 mr-2 inline" />
              {t('agent_showcase.badge')}
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
              {t('agent_showcase.title')}{" "}
              <span className="text-[#3018ef]">
                {t('agent_showcase.title_highlight')}
              </span>{" "}
              {t('agent_showcase.title_end')}
            </h2>

            <p className="text-lg font-normal text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t('agent_showcase.subtitle')}
            </p>
          </div>
        </div>

        {/* Agent Showcase with Circular Testimonials */}
        <div
          className={`transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '400ms' }}
        >
          <div className="flex justify-center">
            <CircularTestimonials
              testimonials={emmaAgents}
              autoplay={true}
              colors={{
                name: "#000",
                designation: "#3018ef",
                testimony: "#4b5563",
                arrowBackground: "#000",
                arrowForeground: "#fff",
                arrowHoverBackground: "#3018ef"
              }}
              fontSizes={{
                name: "1.75rem",
                designation: "1rem",
                quote: "1.125rem"
              }}
            />
          </div>
        </div>

        {/* Agent Capabilities Grid */}
        <div
          className={`mt-16 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 p-12">
            <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
              {t('agent_showcase.capabilities_title')}
            </h3>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-5xl mb-4">🎯</div>
                <h4 className="font-bold text-xl mb-4">{t('agent_showcase.specialization.title')}</h4>
                <p className="text-base text-gray-600 font-medium">{t('agent_showcase.specialization.description')}</p>
              </div>
              <div className="text-center">
                <div className="text-5xl mb-4">⚡</div>
                <h4 className="font-bold text-xl mb-4">{t('agent_showcase.speed.title')}</h4>
                <p className="text-base text-gray-600 font-medium">{t('agent_showcase.speed.description')}</p>
              </div>
              <div className="text-center">
                <div className="text-5xl mb-4">🤝</div>
                <h4 className="font-bold text-xl mb-4">{t('agent_showcase.collaboration.title')}</h4>
                <p className="text-base text-gray-600 font-medium">{t('agent_showcase.collaboration.description')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Stats */}
        <div
          className={`mt-8 grid md:grid-cols-4 gap-6 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '700ms' }}
        >
          {[
            { value: "8", label: t('agent_showcase.stats.agents'), icon: "🤖" },
            { value: "24/7", label: t('agent_showcase.stats.availability'), icon: "⏰" },
            { value: "4.6★", label: t('agent_showcase.stats.rating'), icon: "⭐" },
            { value: "1,326", label: t('agent_showcase.stats.reviews'), icon: "💬" }
          ].map((stat, index) => (
            <div
              key={index}
              className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl hover:scale-105 transition-all duration-300"
            >
              <div className="text-4xl mb-4">{stat.icon}</div>
              <div className="text-3xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2">{stat.value}</div>
              <div className="text-sm font-semibold text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Referencia a todos los agentes del marketplace */}
        <div
          className={`mt-16 text-center transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '800ms' }}
        >
          <div className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              {t('agent_showcase.more_specialists.title')}
            </h3>
            <p className="text-lg text-gray-600 mb-8 max-w-3xl mx-auto font-medium">
              {t('agent_showcase.more_specialists.description')} <span className="font-bold text-[#3018ef]">{t('agent_showcase.more_specialists.description_highlight')}</span> {t('agent_showcase.more_specialists.description_end')}
            </p>

            <div className="grid md:grid-cols-3 gap-6 mb-8 max-w-4xl mx-auto">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100 p-6 hover:scale-105 transition-transform duration-300">
                <div className="text-3xl mb-3">📊</div>
                <h4 className="font-bold text-base mb-2">{t('agent_showcase.more_specialists.analytics_title')}</h4>
                <p className="text-sm text-gray-600 font-medium">{t('agent_showcase.more_specialists.analytics_description')}</p>
              </div>
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100 p-6 hover:scale-105 transition-transform duration-300">
                <div className="text-3xl mb-3">🎨</div>
                <h4 className="font-bold text-base mb-2">{t('agent_showcase.more_specialists.design_title')}</h4>
                <p className="text-sm text-gray-600 font-medium">{t('agent_showcase.more_specialists.design_description')}</p>
              </div>
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100 p-6 hover:scale-105 transition-transform duration-300">
                <div className="text-3xl mb-3">✉️</div>
                <h4 className="font-bold text-base mb-2">{t('agent_showcase.more_specialists.email_title')}</h4>
                <p className="text-sm text-gray-600 font-medium">{t('agent_showcase.more_specialists.email_description')}</p>
              </div>
            </div>

            <Button
              variant="emma_blue"
              size="lg"
              onClick={() => window.open('/profesionales-ia', '_blank')}
            >
              {t('agent_showcase.more_specialists.explore_button')}
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
