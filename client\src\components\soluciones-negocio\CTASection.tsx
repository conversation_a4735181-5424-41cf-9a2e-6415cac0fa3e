import React from "react";
import { motion } from "framer-motion";
import { <PERSON>R<PERSON>, <PERSON> } from "lucide-react";
import { <PERSON> } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

const CTASection: React.FC = () => {
  const { t } = useLanguage();

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] rounded-3xl overflow-hidden shadow-2xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="p-8 md:p-12 lg:p-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl md:text-4xl font-black mb-6 text-white leading-tight">
                  {t('soluciones_negocio.cta.title')}
                </h2>
                <p className="text-xl text-white/90 mb-8 max-w-lg">
                  {t('soluciones_negocio.cta.subtitle')}
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/demo">
                    <motion.button
                      className="bg-white/20 backdrop-blur-md text-white font-bold py-4 px-8 rounded-3xl border border-white/30 shadow-xl hover:bg-white/30 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <span className="flex items-center justify-center gap-2">
                        {t('soluciones_negocio.cta.start_now')} <Rocket size={20} />
                      </span>
                    </motion.button>
                  </Link>
                  <Link href="/contacto">
                    <motion.button
                      className="bg-transparent text-white font-bold py-4 px-8 rounded-3xl border-2 border-white hover:bg-white/10 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <span className="flex items-center justify-center gap-2">
                        {t('soluciones_negocio.cta.talk_to_sales')} <ArrowRight size={20} />
                      </span>
                    </motion.button>
                  </Link>
                </div>
              </motion.div>
            </div>
            
            <div className="relative hidden lg:block">
              <div className="absolute inset-0 bg-black/20"></div>
              <img
                src="/Emma agents.png"
                alt={t('soluciones_negocio.cta.image_alt')}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        {/* La sección de Planes y Precios ha sido eliminada */}
      </div>
    </section>
  );
};

export default CTASection;
